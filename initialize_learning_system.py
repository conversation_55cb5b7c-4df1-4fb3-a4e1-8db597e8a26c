#!/usr/bin/env python3
"""
Learning System Initialization Script
Initializes the persistent learning system for Agentic ORM
"""

import asyncio
import sys
import os
from pathlib import Path
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def initialize_learning_system():
    """Initialize the persistent learning system"""
    
    print("🧠 Initializing Agentic ORM Learning System")
    print("=" * 50)
    
    try:
        # Import learning components
        from app.learning.persistent_learning_engine import learning_engine
        from app.learning.model_fine_tuner import model_fine_tuner
        from config.settings import settings
        
        print("📦 Imported learning components successfully")
        
        # Create necessary directories
        print("\n📁 Creating directory structure...")
        directories = [
            Path(settings.knowledge_base_path),
            Path(settings.learning_checkpoints_path),
            Path(settings.fine_tuned_models_path),
            Path(settings.knowledge_base_path) / "backups"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory}")
            
        # Initialize learning engine
        print("\n🔧 Initializing Learning Engine...")
        await learning_engine.initialize()
        print("   ✅ Learning engine initialized")
        
        # Initialize model fine-tuner
        print("\n🔧 Initializing Model Fine-tuner...")
        await model_fine_tuner.initialize()
        print("   ✅ Model fine-tuner initialized")
        
        # Create initial domain knowledge if not exists
        domain_knowledge_path = Path(settings.knowledge_base_path) / "domain_knowledge.json"
        if not domain_knowledge_path.exists():
            print("\n📚 Creating initial domain knowledge...")
            
            initial_domain_knowledge = {
                "policies": {
                    "baggage_delay_12h": "Compensation up to $50 for delays over 12 hours",
                    "flight_delay_3h": "Meal vouchers for delays over 3 hours",
                    "flight_cancellation": "Full refund or rebooking within 24 hours",
                    "overbooking": "Compensation up to $1350 based on delay duration",
                    "damaged_baggage": "Repair or replacement cost coverage up to $3500"
                },
                "common_issues": {
                    "baggage_lost": "baggage, luggage, lost, missing, suitcase",
                    "baggage_delayed": "baggage, delayed, waiting, arrived, missing",
                    "baggage_damaged": "baggage, damaged, broken, torn, destroyed",
                    "flight_delay": "delay, delayed, late, waiting, departure",
                    "flight_cancellation": "cancel, cancelled, canceled, cancellation",
                    "poor_service": "rude, unprofessional, terrible, awful, horrible",
                    "food_complaint": "food, meal, terrible, cold, inedible",
                    "seat_issues": "seat, uncomfortable, broken, dirty, cramped",
                    "boarding_issues": "boarding, gate, delayed, chaotic, disorganized"
                },
                "escalation_triggers": [
                    "lawsuit", "legal action", "discrimination", "racism",
                    "injury", "medical emergency", "safety concern",
                    "disability", "accessibility", "pregnant",
                    "child", "infant", "elderly", "wheelchair"
                ],
                "compensation_guidelines": {
                    "flight_delay_3h": "meal_voucher",
                    "flight_delay_6h": "hotel_accommodation",
                    "flight_delay_overnight": "hotel_and_transportation",
                    "baggage_delay_12h": "compensation_50",
                    "baggage_delay_24h": "compensation_100",
                    "baggage_lost": "compensation_up_to_3500",
                    "overbooking_1_2h": "compensation_200_percent",
                    "overbooking_2h_plus": "compensation_400_percent"
                },
                "language_preferences": {
                    "en": "English - Professional and empathetic tone",
                    "es": "Spanish - Formal and respectful tone",
                    "fr": "French - Polite and courteous tone",
                    "de": "German - Direct but friendly tone",
                    "it": "Italian - Warm and personal tone",
                    "pt": "Portuguese - Respectful and helpful tone",
                    "ja": "Japanese - Very polite and formal tone",
                    "ko": "Korean - Respectful and humble tone",
                    "zh": "Chinese - Respectful and professional tone",
                    "ar": "Arabic - Formal and respectful tone",
                    "hi": "Hindi - Respectful and helpful tone",
                    "ta": "Tamil - Respectful and empathetic tone"
                },
                "sentiment_patterns": {
                    "angry": ["furious", "outraged", "disgusted", "terrible", "horrible", "worst"],
                    "frustrated": ["annoyed", "disappointed", "upset", "irritated", "bothered"],
                    "urgent": ["emergency", "urgent", "asap", "immediately", "critical"],
                    "sad": ["disappointed", "heartbroken", "devastated", "upset", "crying"],
                    "confused": ["confused", "unclear", "don't understand", "help", "explain"]
                },
                "response_templates": {
                    "apology_empathetic": "We sincerely apologize for the inconvenience and understand how frustrating this must be for you.",
                    "apology_professional": "We apologize for the disruption to your travel plans.",
                    "investigation": "We're looking into this matter immediately and will provide an update shortly.",
                    "compensation_offer": "We'd like to make this right. Please DM us your booking details so we can review compensation options.",
                    "escalation": "This requires immediate attention. Please DM us your details and we'll have a supervisor contact you.",
                    "follow_up": "Thank you for bringing this to our attention. We'll follow up with you within 24 hours."
                }
            }
            
            with open(domain_knowledge_path, 'w', encoding='utf-8') as f:
                json.dump(initial_domain_knowledge, f, indent=2, ensure_ascii=False)
                
            print("   ✅ Initial domain knowledge created")
        
        # Create initial checkpoint
        print("\n📊 Creating initial checkpoint...")
        await learning_engine._create_checkpoint(0)
        print("   ✅ Initial checkpoint created")
        
        # Get learning statistics
        print("\n📈 Learning System Statistics:")
        stats = await learning_engine.get_learning_statistics()
        
        print(f"   📚 Total Learning Rules: {stats.get('total_rules', 0)}")
        print(f"   🎯 Average Confidence: {stats.get('avg_confidence', 0):.2f}")
        print(f"   📊 Success Rate: {stats.get('avg_success_rate', 0):.2f}")
        print(f"   🔄 Total Usage: {stats.get('total_usage', 0)}")
        
        if stats.get('current_checkpoint'):
            checkpoint = stats['current_checkpoint']
            print(f"   📋 Current Checkpoint: {checkpoint.get('checkpoint_id', 'N/A')}")
            print(f"   🕒 Timestamp: {checkpoint.get('timestamp', 'N/A')}")
        
        # Test learning application
        print("\n🧪 Testing Learning Application...")
        test_result = await learning_engine.apply_learning_to_response(
            "My baggage is delayed and I'm very frustrated!",
            {"sentiment": "negative", "issue_type": "baggage_delay"}
        )
        
        if test_result:
            print("   ✅ Learning application test successful")
            print(f"   💬 Sample learned response: {test_result[:100]}...")
        else:
            print("   ℹ️  No specific learning rules available yet (this is normal for new systems)")
        
        print("\n🎉 Learning System Initialization Complete!")
        print("\n📋 Next Steps:")
        print("   1. Start collecting feedback from users")
        print("   2. Process feedback: POST /api/v1/learning/process-feedback")
        print("   3. Monitor learning: GET /api/v1/learning/statistics")
        print("   4. Create backups: POST /api/v1/learning/knowledge-base/backup")
        
        print("\n🔧 Configuration:")
        print(f"   📁 Knowledge Base: {settings.knowledge_base_path}")
        print(f"   📊 Checkpoints: {settings.learning_checkpoints_path}")
        print(f"   🤖 Fine-tuned Models: {settings.fine_tuned_models_path}")
        print(f"   ⚙️  Learning Strategy: {getattr(settings, 'learning_retention_strategy', 'hybrid')}")
        print(f"   🔄 Auto-save: {getattr(settings, 'auto_save_learning', True)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ Initialization Error: {e}")
        print("   Check your configuration and try again")
        return False


async def verify_learning_system():
    """Verify that the learning system is working correctly"""
    
    print("\n🔍 Verifying Learning System...")
    
    try:
        from app.learning.persistent_learning_engine import learning_engine
        
        # Check if initialized
        if not learning_engine.is_initialized:
            print("   ⚠️  Learning engine not initialized")
            return False
            
        # Check knowledge base files
        knowledge_base_path = Path(learning_engine.knowledge_base_path)
        rulebook_path = Path(learning_engine.rulebook_path)
        
        if knowledge_base_path.exists():
            print("   ✅ Knowledge base directory exists")
        else:
            print("   ❌ Knowledge base directory missing")
            return False
            
        if rulebook_path.exists():
            print("   ✅ Rulebook file exists")
        else:
            print("   ℹ️  Rulebook file will be created when first rules are learned")
            
        # Check checkpoints
        checkpoints_path = Path(learning_engine.checkpoints_path)
        if checkpoints_path.exists():
            checkpoint_files = list(checkpoints_path.glob("checkpoint_*.json"))
            print(f"   ✅ Checkpoints directory exists ({len(checkpoint_files)} checkpoints)")
        else:
            print("   ❌ Checkpoints directory missing")
            return False
            
        print("   ✅ Learning system verification passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Verification failed: {e}")
        return False


def main():
    """Main function"""
    
    print("🚀 Agentic ORM Learning System Initializer")
    print("==========================================")
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Error: Please run this script from the project root directory")
        print("   Current directory should contain 'app' folder")
        sys.exit(1)
        
    # Check if .env file exists
    if not Path(".env").exists():
        print("❌ Error: .env file not found")
        print("   Please create .env file with learning configuration")
        sys.exit(1)
        
    # Run initialization
    try:
        success = asyncio.run(initialize_learning_system())
        
        if success:
            # Run verification
            verification_success = asyncio.run(verify_learning_system())
            
            if verification_success:
                print("\n🎉 SUCCESS: Learning system is ready!")
                print("\n📚 Documentation: See LEARNING_PERSISTENCE_GUIDE.md")
                print("🌐 API Docs: http://localhost:8000/docs (when server is running)")
                sys.exit(0)
            else:
                print("\n⚠️  WARNING: Initialization completed but verification failed")
                sys.exit(1)
        else:
            print("\n❌ FAILED: Learning system initialization failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Initialization interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
