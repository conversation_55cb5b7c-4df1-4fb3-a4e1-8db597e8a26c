"""
Model Fine-tuning Component for Persistent Learning
Handles incremental fine-tuning of Phi-3 Mini model with learned data
"""

import os
import json
import torch
import structlog
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    TrainingArguments, 
    Trainer,
    DataCollatorForLanguageModeling,
    EarlyStoppingCallback
)
from datasets import Dataset
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.feedback import Feedback
from app.models.generated_response import GeneratedResponse
from app.models.social_post import SocialPost
from config.settings import settings

logger = structlog.get_logger()


@dataclass
class FineTuningConfig:
    """Configuration for fine-tuning process"""
    model_name: str = "microsoft/Phi-3-mini-4k-instruct"
    max_length: int = 512
    batch_size: int = 4
    learning_rate: float = 5e-5
    num_epochs: int = 3
    warmup_steps: int = 100
    save_steps: int = 500
    eval_steps: int = 250
    gradient_accumulation_steps: int = 4
    fp16: bool = True
    dataloader_num_workers: int = 2


class ModelFineTuner:
    """Handles incremental fine-tuning of the local model"""
    
    def __init__(self, config: Optional[FineTuningConfig] = None):
        self.config = config or FineTuningConfig()
        self.fine_tuned_path = Path(settings.fine_tuned_models_path)
        self.training_data_path = self.fine_tuned_path / "training_data"
        
        # Model components
        self.tokenizer = None
        self.model = None
        self.trainer = None
        
        # Training state
        self.is_training = False
        self.last_training_time = None
        
        # Create directories
        self.fine_tuned_path.mkdir(parents=True, exist_ok=True)
        self.training_data_path.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """Initialize the fine-tuner"""
        try:
            logger.info("🔧 Initializing Model Fine-tuner...")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_name,
                cache_dir=settings.phi3_cache_dir
            )
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            logger.info("✅ Model Fine-tuner initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize fine-tuner: {e}")
            raise
            
    async def prepare_training_data(self, db: AsyncSession) -> Dict[str, Any]:
        """Prepare training data from feedback"""
        try:
            logger.info("📊 Preparing training data from feedback...")
            
            # Get positive feedback with high ratings
            positive_query = select(Feedback, GeneratedResponse, SocialPost).join(
                GeneratedResponse, Feedback.response_id == GeneratedResponse.id
            ).join(
                SocialPost, GeneratedResponse.post_id == SocialPost.id
            ).where(
                (Feedback.feedback_type == "thumbs_up") | 
                (Feedback.rating >= 4)
            )
            
            positive_result = await db.execute(positive_query)
            positive_samples = positive_result.all()
            
            # Get negative feedback with suggested improvements
            improvement_query = select(Feedback, GeneratedResponse, SocialPost).join(
                GeneratedResponse, Feedback.response_id == GeneratedResponse.id
            ).join(
                SocialPost, GeneratedResponse.post_id == SocialPost.id
            ).where(
                Feedback.suggested_response.isnot(None)
            )
            
            improvement_result = await db.execute(improvement_query)
            improvement_samples = improvement_result.all()
            
            # Prepare training examples
            training_examples = []
            
            # Add positive examples (reinforce good responses)
            for feedback, response, post in positive_samples:
                example = self._create_training_example(
                    post.content, 
                    response.response_text,
                    "positive"
                )
                training_examples.append(example)
                
            # Add improvement examples (learn better responses)
            for feedback, response, post in improvement_samples:
                example = self._create_training_example(
                    post.content,
                    feedback.suggested_response,
                    "improvement"
                )
                training_examples.append(example)
                
            # Save training data
            training_file = self.training_data_path / f"training_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(training_file, 'w', encoding='utf-8') as f:
                json.dump(training_examples, f, indent=2, ensure_ascii=False)
                
            logger.info(f"📊 Prepared {len(training_examples)} training examples")
            
            return {
                "success": True,
                "total_examples": len(training_examples),
                "positive_examples": len(positive_samples),
                "improvement_examples": len(improvement_samples),
                "training_file": str(training_file)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare training data: {e}")
            return {"success": False, "error": str(e)}
            
    def _create_training_example(
        self, 
        input_text: str, 
        target_response: str, 
        example_type: str
    ) -> Dict[str, str]:
        """Create a training example in the correct format"""
        
        # Create instruction-following format for Phi-3
        system_prompt = """You are an AI assistant for airline customer service. 
Respond professionally, empathetically, and helpfully to customer inquiries and complaints."""
        
        user_message = f"Customer message: {input_text}"
        assistant_response = target_response
        
        # Format as conversation
        conversation = f"<|system|>\n{system_prompt}<|end|>\n<|user|>\n{user_message}<|end|>\n<|assistant|>\n{assistant_response}<|end|>"
        
        return {
            "text": conversation,
            "input": input_text,
            "output": target_response,
            "type": example_type,
            "created_at": datetime.now().isoformat()
        }
        
    async def fine_tune_model(self, training_data_file: str) -> Dict[str, Any]:
        """Fine-tune the model with prepared data"""
        try:
            if self.is_training:
                return {"success": False, "error": "Training already in progress"}
                
            self.is_training = True
            logger.info("🔄 Starting model fine-tuning...")
            
            # Load training data
            with open(training_data_file, 'r', encoding='utf-8') as f:
                training_examples = json.load(f)
                
            if len(training_examples) < 10:
                return {
                    "success": False, 
                    "error": f"Insufficient training data: {len(training_examples)} examples"
                }
                
            # Load base model
            model = AutoModelForCausalLM.from_pretrained(
                self.config.model_name,
                cache_dir=settings.phi3_cache_dir,
                torch_dtype=torch.float16 if self.config.fp16 else torch.float32,
                device_map="auto"
            )
            
            # Prepare dataset
            dataset = self._prepare_dataset(training_examples)
            
            # Split into train/validation
            train_size = int(0.9 * len(dataset))
            train_dataset = dataset.select(range(train_size))
            eval_dataset = dataset.select(range(train_size, len(dataset)))
            
            # Setup training arguments
            output_dir = self.fine_tuned_path / f"phi3_finetuned_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            training_args = TrainingArguments(
                output_dir=str(output_dir),
                num_train_epochs=self.config.num_epochs,
                per_device_train_batch_size=self.config.batch_size,
                per_device_eval_batch_size=self.config.batch_size,
                gradient_accumulation_steps=self.config.gradient_accumulation_steps,
                learning_rate=self.config.learning_rate,
                warmup_steps=self.config.warmup_steps,
                logging_steps=50,
                save_steps=self.config.save_steps,
                eval_steps=self.config.eval_steps,
                evaluation_strategy="steps",
                save_strategy="steps",
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                fp16=self.config.fp16,
                dataloader_num_workers=self.config.dataloader_num_workers,
                remove_unused_columns=False,
                report_to=None,  # Disable wandb/tensorboard
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=self.tokenizer,
                mlm=False,
                pad_to_multiple_of=8
            )
            
            # Create trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                data_collator=data_collator,
                callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
            )
            
            # Start training
            training_result = trainer.train()
            
            # Save the fine-tuned model
            trainer.save_model()
            self.tokenizer.save_pretrained(str(output_dir))
            
            # Save training metadata
            metadata = {
                "training_completed": datetime.now().isoformat(),
                "training_examples": len(training_examples),
                "train_samples": len(train_dataset),
                "eval_samples": len(eval_dataset),
                "final_train_loss": float(training_result.training_history[-1]["train_loss"]),
                "final_eval_loss": float(training_result.training_history[-1]["eval_loss"]),
                "base_model": self.config.model_name,
                "config": self.config.__dict__
            }
            
            with open(output_dir / "training_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
                
            self.last_training_time = datetime.now()
            self.is_training = False
            
            logger.info(f"✅ Fine-tuning completed: {output_dir}")
            
            return {
                "success": True,
                "model_path": str(output_dir),
                "training_examples": len(training_examples),
                "final_train_loss": metadata["final_train_loss"],
                "final_eval_loss": metadata["final_eval_loss"]
            }
            
        except Exception as e:
            self.is_training = False
            logger.error(f"❌ Fine-tuning failed: {e}")
            return {"success": False, "error": str(e)}
            
    def _prepare_dataset(self, training_examples: List[Dict]) -> Dataset:
        """Prepare dataset for training"""
        
        # Tokenize examples
        def tokenize_function(examples):
            # Tokenize the text
            tokenized = self.tokenizer(
                examples["text"],
                truncation=True,
                padding=True,
                max_length=self.config.max_length,
                return_tensors="pt"
            )
            
            # For causal LM, labels are the same as input_ids
            tokenized["labels"] = tokenized["input_ids"].clone()
            
            return tokenized
            
        # Create dataset
        texts = [example["text"] for example in training_examples]
        dataset = Dataset.from_dict({"text": texts})
        
        # Tokenize
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        return tokenized_dataset
        
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available fine-tuned models"""
        try:
            models = []
            
            for model_dir in self.fine_tuned_path.iterdir():
                if model_dir.is_dir() and model_dir.name.startswith("phi3_finetuned_"):
                    metadata_file = model_dir / "training_metadata.json"
                    
                    if metadata_file.exists():
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                            
                        models.append({
                            "name": model_dir.name,
                            "path": str(model_dir),
                            "created": metadata.get("training_completed"),
                            "training_examples": metadata.get("training_examples", 0),
                            "final_loss": metadata.get("final_eval_loss", 0),
                            "base_model": metadata.get("base_model")
                        })
                        
            # Sort by creation date (newest first)
            models.sort(key=lambda x: x["created"], reverse=True)
            
            return models
            
        except Exception as e:
            logger.error(f"❌ Failed to get available models: {e}")
            return []


# Global instance
model_fine_tuner = ModelFineTuner()
