"""
Persistent Learning Engine for Agentic ORM
Ensures learning is retained across application restarts and model upgrades
"""

import json
import os
import pickle
import asyncio
import structlog
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer
import numpy as np

from app.models.feedback import Feedback
from app.models.generated_response import GeneratedResponse
from app.models.social_post import SocialPost
from config.settings import settings

logger = structlog.get_logger()


@dataclass
class LearningRule:
    """Represents a learned rule from feedback"""
    rule_id: str
    pattern: str  # Text pattern that triggers this rule
    context_type: str  # complaint, compliment, inquiry, etc.
    response_template: str
    confidence_score: float
    usage_count: int
    success_rate: float
    created_at: str
    last_updated: str
    feedback_sources: List[int]  # Feedback IDs that contributed to this rule


@dataclass
class DomainKnowledge:
    """Domain-specific knowledge for airline customer service"""
    policies: Dict[str, Any]
    common_issues: Dict[str, str]
    escalation_triggers: List[str]
    compensation_guidelines: Dict[str, Any]
    language_preferences: Dict[str, str]
    sentiment_patterns: Dict[str, List[str]]


@dataclass
class LearningCheckpoint:
    """Checkpoint for incremental learning"""
    checkpoint_id: str
    model_version: str
    timestamp: str
    rules_count: int
    feedback_processed: int
    performance_metrics: Dict[str, float]
    knowledge_hash: str


class PersistentLearningEngine:
    """Main engine for persistent learning across restarts and upgrades"""
    
    def __init__(self):
        self.knowledge_base_path = Path(settings.knowledge_base_path)
        self.rulebook_path = Path(settings.rulebook_path)
        self.checkpoints_path = Path(settings.learning_checkpoints_path)
        self.fine_tuned_path = Path(settings.fine_tuned_models_path)
        
        # In-memory caches
        self.learning_rules: Dict[str, LearningRule] = {}
        self.domain_knowledge: Optional[DomainKnowledge] = None
        self.current_checkpoint: Optional[LearningCheckpoint] = None
        
        # Learning state
        self.is_initialized = False
        self.last_backup_time = None
        
        # Create directories
        self._create_directories()
        
    def _create_directories(self):
        """Create necessary directories for learning persistence"""
        for path in [self.knowledge_base_path, self.checkpoints_path, self.fine_tuned_path]:
            path.mkdir(parents=True, exist_ok=True)
            
    async def initialize(self):
        """Initialize the learning engine"""
        try:
            logger.info("🧠 Initializing Persistent Learning Engine...")
            
            # Load existing knowledge base
            await self._load_knowledge_base()
            
            # Load domain knowledge
            await self._load_domain_knowledge()
            
            # Load latest checkpoint
            await self._load_latest_checkpoint()
            
            # Start background tasks
            if settings.auto_save_learning:
                asyncio.create_task(self._periodic_backup())
                
            self.is_initialized = True
            logger.info("✅ Persistent Learning Engine initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize learning engine: {e}")
            raise
            
    async def _load_knowledge_base(self):
        """Load the persistent knowledge base (rulebook)"""
        try:
            if self.rulebook_path.exists():
                with open(self.rulebook_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Convert to LearningRule objects
                for rule_data in data.get('rules', []):
                    rule = LearningRule(**rule_data)
                    self.learning_rules[rule.rule_id] = rule
                    
                logger.info(f"📚 Loaded {len(self.learning_rules)} learning rules from knowledge base")
            else:
                logger.info("📚 No existing knowledge base found, starting fresh")
                
        except Exception as e:
            logger.error(f"❌ Failed to load knowledge base: {e}")
            
    async def _load_domain_knowledge(self):
        """Load domain-specific knowledge"""
        domain_path = self.knowledge_base_path / "domain_knowledge.json"
        
        try:
            if domain_path.exists():
                with open(domain_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.domain_knowledge = DomainKnowledge(**data)
            else:
                # Initialize with default airline domain knowledge
                self.domain_knowledge = DomainKnowledge(
                    policies={
                        "baggage_delay": "Compensation up to $50 for delays over 12 hours",
                        "flight_delay": "Meal vouchers for delays over 3 hours",
                        "cancellation": "Full refund or rebooking within 24 hours"
                    },
                    common_issues={
                        "baggage_lost": "baggage, luggage, lost, missing",
                        "flight_delay": "delay, delayed, late, waiting",
                        "poor_service": "rude, unprofessional, terrible, awful"
                    },
                    escalation_triggers=[
                        "lawsuit", "legal action", "discrimination", 
                        "injury", "medical emergency", "safety"
                    ],
                    compensation_guidelines={
                        "flight_delay_3h": "meal_voucher",
                        "flight_delay_6h": "hotel_accommodation",
                        "baggage_delay_12h": "compensation_50"
                    },
                    language_preferences={},
                    sentiment_patterns={
                        "angry": ["furious", "outraged", "disgusted", "terrible"],
                        "frustrated": ["annoyed", "disappointed", "upset"],
                        "urgent": ["emergency", "urgent", "asap", "immediately"]
                    }
                )
                await self._save_domain_knowledge()
                
        except Exception as e:
            logger.error(f"❌ Failed to load domain knowledge: {e}")
            
    async def _load_latest_checkpoint(self):
        """Load the latest learning checkpoint"""
        try:
            checkpoint_files = list(self.checkpoints_path.glob("checkpoint_*.json"))
            if checkpoint_files:
                latest_file = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
                
                with open(latest_file, 'r') as f:
                    data = json.load(f)
                    self.current_checkpoint = LearningCheckpoint(**data)
                    
                logger.info(f"📊 Loaded checkpoint: {self.current_checkpoint.checkpoint_id}")
                
        except Exception as e:
            logger.error(f"❌ Failed to load checkpoint: {e}")

    async def process_feedback_for_learning(self, db: AsyncSession) -> Dict[str, Any]:
        """Process unprocessed feedback to extract learning rules"""
        try:
            # Get unprocessed feedback
            query = select(Feedback).where(Feedback.is_processed_for_learning == False)
            result = await db.execute(query)
            unprocessed_feedback = result.scalars().all()

            if len(unprocessed_feedback) < settings.min_feedback_samples:
                return {
                    "status": "insufficient_data",
                    "message": f"Need at least {settings.min_feedback_samples} feedback samples",
                    "current_count": len(unprocessed_feedback)
                }

            logger.info(f"🔄 Processing {len(unprocessed_feedback)} feedback samples for learning")

            new_rules_count = 0
            updated_rules_count = 0

            for feedback in unprocessed_feedback:
                # Get the associated response and post
                response_query = select(GeneratedResponse).where(
                    GeneratedResponse.id == feedback.response_id
                )
                response_result = await db.execute(response_query)
                response = response_result.scalar_one_or_none()

                if not response:
                    continue

                post_query = select(SocialPost).where(
                    SocialPost.id == response.post_id
                )
                post_result = await db.execute(post_query)
                post = post_result.scalar_one_or_none()

                if not post:
                    continue

                # Extract learning from this feedback
                rule_result = await self._extract_learning_rule(feedback, response, post)

                if rule_result:
                    if rule_result["is_new"]:
                        new_rules_count += 1
                    else:
                        updated_rules_count += 1

                # Mark as processed
                feedback.is_processed_for_learning = True

            await db.commit()

            # Save updated knowledge base
            await self._save_knowledge_base()

            # Create new checkpoint
            await self._create_checkpoint(len(unprocessed_feedback))

            return {
                "status": "success",
                "processed_count": len(unprocessed_feedback),
                "new_rules": new_rules_count,
                "updated_rules": updated_rules_count,
                "total_rules": len(self.learning_rules)
            }

        except Exception as e:
            logger.error(f"❌ Failed to process feedback for learning: {e}")
            return {"status": "error", "message": str(e)}

    async def _extract_learning_rule(
        self,
        feedback: Feedback,
        response: GeneratedResponse,
        post: SocialPost
    ) -> Optional[Dict[str, Any]]:
        """Extract a learning rule from feedback data"""
        try:
            # Analyze the feedback to determine what to learn
            if feedback.feedback_type == "thumbs_down" or (feedback.rating and feedback.rating < 3):
                # Negative feedback - learn what NOT to do
                return await self._extract_negative_learning(feedback, response, post)
            elif feedback.feedback_type == "thumbs_up" or (feedback.rating and feedback.rating >= 4):
                # Positive feedback - reinforce good patterns
                return await self._extract_positive_learning(feedback, response, post)
            elif feedback.suggested_response:
                # Suggested improvement - learn better response
                return await self._extract_improvement_learning(feedback, response, post)

        except Exception as e:
            logger.error(f"❌ Failed to extract learning rule: {e}")
            return None

    async def _extract_negative_learning(
        self,
        feedback: Feedback,
        response: GeneratedResponse,
        post: SocialPost
    ) -> Optional[Dict[str, Any]]:
        """Extract learning from negative feedback"""
        # Identify patterns to avoid
        issue_pattern = self._identify_issue_pattern(post.content)
        response_pattern = self._identify_response_pattern(response.response_text)

        rule_id = f"avoid_{issue_pattern}_{response_pattern}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create or update avoidance rule
        if rule_id not in self.learning_rules:
            rule = LearningRule(
                rule_id=rule_id,
                pattern=issue_pattern,
                context_type=post.sentiment_label or "neutral",
                response_template=f"AVOID: {response_pattern}",
                confidence_score=0.7,
                usage_count=0,
                success_rate=0.0,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
                feedback_sources=[feedback.id]
            )
            self.learning_rules[rule_id] = rule
            return {"is_new": True, "rule": rule}
        else:
            # Update existing rule
            existing_rule = self.learning_rules[rule_id]
            existing_rule.feedback_sources.append(feedback.id)
            existing_rule.last_updated = datetime.now().isoformat()
            existing_rule.confidence_score = min(0.95, existing_rule.confidence_score + 0.1)
            return {"is_new": False, "rule": existing_rule}

    async def _extract_positive_learning(
        self,
        feedback: Feedback,
        response: GeneratedResponse,
        post: SocialPost
    ) -> Optional[Dict[str, Any]]:
        """Extract learning from positive feedback"""
        issue_pattern = self._identify_issue_pattern(post.content)
        response_pattern = self._identify_response_pattern(response.response_text)

        rule_id = f"good_{issue_pattern}_{response_pattern}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        if rule_id not in self.learning_rules:
            rule = LearningRule(
                rule_id=rule_id,
                pattern=issue_pattern,
                context_type=post.sentiment_label or "neutral",
                response_template=response.response_text,
                confidence_score=0.8,
                usage_count=1,
                success_rate=1.0,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
                feedback_sources=[feedback.id]
            )
            self.learning_rules[rule_id] = rule
            return {"is_new": True, "rule": rule}
        else:
            # Update existing rule
            existing_rule = self.learning_rules[rule_id]
            existing_rule.feedback_sources.append(feedback.id)
            existing_rule.usage_count += 1
            existing_rule.success_rate = (existing_rule.success_rate + 1.0) / 2
            existing_rule.confidence_score = min(0.95, existing_rule.confidence_score + 0.05)
            existing_rule.last_updated = datetime.now().isoformat()
            return {"is_new": False, "rule": existing_rule}

    async def _extract_improvement_learning(
        self,
        feedback: Feedback,
        response: GeneratedResponse,
        post: SocialPost
    ) -> Optional[Dict[str, Any]]:
        """Extract learning from suggested improvements"""
        if not feedback.suggested_response:
            return None

        issue_pattern = self._identify_issue_pattern(post.content)

        rule_id = f"improved_{issue_pattern}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        rule = LearningRule(
            rule_id=rule_id,
            pattern=issue_pattern,
            context_type=post.sentiment_label or "neutral",
            response_template=feedback.suggested_response,
            confidence_score=0.9,  # High confidence for human suggestions
            usage_count=0,
            success_rate=0.0,
            created_at=datetime.now().isoformat(),
            last_updated=datetime.now().isoformat(),
            feedback_sources=[feedback.id]
        )

        self.learning_rules[rule_id] = rule
        return {"is_new": True, "rule": rule}

    def _identify_issue_pattern(self, content: str) -> str:
        """Identify the type of issue from post content"""
        content_lower = content.lower()

        # Check against domain knowledge
        if self.domain_knowledge:
            for issue_type, keywords in self.domain_knowledge.common_issues.items():
                if any(keyword in content_lower for keyword in keywords.split(', ')):
                    return issue_type

        # Fallback pattern extraction
        if any(word in content_lower for word in ['bag', 'luggage', 'suitcase']):
            return "baggage_issue"
        elif any(word in content_lower for word in ['delay', 'late', 'waiting']):
            return "delay_issue"
        elif any(word in content_lower for word in ['cancel', 'cancelled']):
            return "cancellation_issue"
        elif any(word in content_lower for word in ['service', 'staff', 'crew']):
            return "service_issue"
        else:
            return "general_issue"

    def _identify_response_pattern(self, response_text: str) -> str:
        """Identify the pattern/type of response"""
        response_lower = response_text.lower()

        if any(word in response_lower for word in ['sorry', 'apologize', 'regret']):
            return "apology_response"
        elif any(word in response_lower for word in ['compensation', 'refund', 'voucher']):
            return "compensation_response"
        elif any(word in response_lower for word in ['investigate', 'look into', 'check']):
            return "investigation_response"
        elif any(word in response_lower for word in ['dm', 'direct message', 'contact']):
            return "escalation_response"
        else:
            return "general_response"

    async def apply_learning_to_response(
        self,
        post_content: str,
        context: Dict[str, Any]
    ) -> Optional[str]:
        """Apply learned rules to generate better responses"""
        try:
            issue_pattern = self._identify_issue_pattern(post_content)
            sentiment = context.get('sentiment', 'neutral')

            # Find matching rules
            matching_rules = []
            for rule in self.learning_rules.values():
                if (rule.pattern == issue_pattern and
                    rule.context_type == sentiment and
                    not rule.response_template.startswith("AVOID:")):
                    matching_rules.append(rule)

            if not matching_rules:
                return None

            # Sort by confidence and success rate
            matching_rules.sort(
                key=lambda r: (r.confidence_score * r.success_rate),
                reverse=True
            )

            best_rule = matching_rules[0]

            # Update usage count
            best_rule.usage_count += 1
            best_rule.last_updated = datetime.now().isoformat()

            logger.info(f"📚 Applied learning rule: {best_rule.rule_id}")

            return best_rule.response_template

        except Exception as e:
            logger.error(f"❌ Failed to apply learning: {e}")
            return None

    async def _save_knowledge_base(self):
        """Save the knowledge base to persistent storage"""
        try:
            # Convert rules to serializable format
            rules_data = []
            for rule in self.learning_rules.values():
                rules_data.append(asdict(rule))

            knowledge_data = {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "rules_count": len(rules_data),
                "rules": rules_data
            }

            # Save to file
            with open(self.rulebook_path, 'w', encoding='utf-8') as f:
                json.dump(knowledge_data, f, indent=2, ensure_ascii=False)

            logger.info(f"💾 Saved knowledge base with {len(rules_data)} rules")

        except Exception as e:
            logger.error(f"❌ Failed to save knowledge base: {e}")

    async def _save_domain_knowledge(self):
        """Save domain knowledge to persistent storage"""
        try:
            domain_path = self.knowledge_base_path / "domain_knowledge.json"

            with open(domain_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.domain_knowledge), f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"❌ Failed to save domain knowledge: {e}")

    async def _create_checkpoint(self, feedback_processed: int):
        """Create a learning checkpoint"""
        try:
            checkpoint_id = f"checkpoint_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Calculate performance metrics
            total_rules = len(self.learning_rules)
            avg_confidence = np.mean([r.confidence_score for r in self.learning_rules.values()]) if total_rules > 0 else 0
            avg_success_rate = np.mean([r.success_rate for r in self.learning_rules.values()]) if total_rules > 0 else 0

            checkpoint = LearningCheckpoint(
                checkpoint_id=checkpoint_id,
                model_version=settings.phi3_model,
                timestamp=datetime.now().isoformat(),
                rules_count=total_rules,
                feedback_processed=feedback_processed,
                performance_metrics={
                    "avg_confidence": float(avg_confidence),
                    "avg_success_rate": float(avg_success_rate),
                    "total_usage": sum(r.usage_count for r in self.learning_rules.values())
                },
                knowledge_hash=self._calculate_knowledge_hash()
            )

            # Save checkpoint
            checkpoint_file = self.checkpoints_path / f"{checkpoint_id}.json"
            with open(checkpoint_file, 'w') as f:
                json.dump(asdict(checkpoint), f, indent=2)

            self.current_checkpoint = checkpoint
            logger.info(f"📊 Created checkpoint: {checkpoint_id}")

        except Exception as e:
            logger.error(f"❌ Failed to create checkpoint: {e}")

    def _calculate_knowledge_hash(self) -> str:
        """Calculate hash of current knowledge for integrity checking"""
        import hashlib

        # Create a string representation of all rules
        rules_str = json.dumps(
            sorted([asdict(rule) for rule in self.learning_rules.values()],
                   key=lambda x: x['rule_id']),
            sort_keys=True
        )

        return hashlib.sha256(rules_str.encode()).hexdigest()[:16]

    async def _periodic_backup(self):
        """Periodic backup of learning data"""
        while True:
            try:
                await asyncio.sleep(settings.learning_backup_interval_hours * 3600)

                if self.last_backup_time is None or \
                   (datetime.now() - self.last_backup_time).total_seconds() > \
                   settings.learning_backup_interval_hours * 3600:

                    await self._save_knowledge_base()
                    await self._save_domain_knowledge()
                    self.last_backup_time = datetime.now()

                    logger.info("💾 Periodic learning backup completed")

            except Exception as e:
                logger.error(f"❌ Periodic backup failed: {e}")

    async def transfer_learning_to_new_model(self, new_model_version: str) -> Dict[str, Any]:
        """Transfer learning when upgrading to a new model version"""
        try:
            logger.info(f"🔄 Transferring learning to new model: {new_model_version}")

            # Create backup of current state
            backup_checkpoint = await self._create_transfer_backup()

            # Update model version in all rules
            for rule in self.learning_rules.values():
                rule.last_updated = datetime.now().isoformat()

            # Create new checkpoint for new model
            await self._create_checkpoint(0)

            # Update current checkpoint model version
            if self.current_checkpoint:
                self.current_checkpoint.model_version = new_model_version

            # Save updated knowledge base
            await self._save_knowledge_base()

            return {
                "success": True,
                "transferred_rules": len(self.learning_rules),
                "backup_checkpoint": backup_checkpoint,
                "new_model_version": new_model_version
            }

        except Exception as e:
            logger.error(f"❌ Failed to transfer learning: {e}")
            return {"success": False, "error": str(e)}

    async def _create_transfer_backup(self) -> str:
        """Create backup before model transfer"""
        backup_id = f"transfer_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = self.knowledge_base_path / "backups" / backup_id
        backup_path.mkdir(parents=True, exist_ok=True)

        # Copy current knowledge base
        import shutil
        shutil.copy2(self.rulebook_path, backup_path / "rulebook.json")
        shutil.copy2(
            self.knowledge_base_path / "domain_knowledge.json",
            backup_path / "domain_knowledge.json"
        )

        # Copy latest checkpoint
        if self.current_checkpoint:
            checkpoint_file = self.checkpoints_path / f"{self.current_checkpoint.checkpoint_id}.json"
            if checkpoint_file.exists():
                shutil.copy2(checkpoint_file, backup_path / "checkpoint.json")

        return backup_id

    async def get_learning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive learning statistics"""
        try:
            if not self.learning_rules:
                return {
                    "total_rules": 0,
                    "status": "no_learning_data"
                }

            # Calculate statistics
            total_rules = len(self.learning_rules)
            avg_confidence = np.mean([r.confidence_score for r in self.learning_rules.values()])
            avg_success_rate = np.mean([r.success_rate for r in self.learning_rules.values()])
            total_usage = sum(r.usage_count for r in self.learning_rules.values())

            # Group by pattern
            pattern_stats = {}
            for rule in self.learning_rules.values():
                pattern = rule.pattern
                if pattern not in pattern_stats:
                    pattern_stats[pattern] = {"count": 0, "avg_confidence": 0}
                pattern_stats[pattern]["count"] += 1
                pattern_stats[pattern]["avg_confidence"] += rule.confidence_score

            # Calculate averages
            for pattern in pattern_stats:
                pattern_stats[pattern]["avg_confidence"] /= pattern_stats[pattern]["count"]

            return {
                "total_rules": total_rules,
                "avg_confidence": float(avg_confidence),
                "avg_success_rate": float(avg_success_rate),
                "total_usage": total_usage,
                "pattern_distribution": pattern_stats,
                "current_checkpoint": asdict(self.current_checkpoint) if self.current_checkpoint else None,
                "knowledge_hash": self._calculate_knowledge_hash(),
                "last_backup": self.last_backup_time.isoformat() if self.last_backup_time else None
            }

        except Exception as e:
            logger.error(f"❌ Failed to get learning statistics: {e}")
            return {"error": str(e)}

    async def cleanup_old_rules(self, max_age_days: int = 90) -> Dict[str, Any]:
        """Clean up old, unused learning rules"""
        try:
            cutoff_date = datetime.now() - timedelta(days=max_age_days)

            rules_to_remove = []
            for rule_id, rule in self.learning_rules.items():
                rule_date = datetime.fromisoformat(rule.last_updated)
                if rule_date < cutoff_date and rule.usage_count == 0:
                    rules_to_remove.append(rule_id)

            # Remove old rules
            for rule_id in rules_to_remove:
                del self.learning_rules[rule_id]

            if rules_to_remove:
                await self._save_knowledge_base()

            return {
                "removed_rules": len(rules_to_remove),
                "remaining_rules": len(self.learning_rules)
            }

        except Exception as e:
            logger.error(f"❌ Failed to cleanup old rules: {e}")
            return {"error": str(e)}


# Global instance
learning_engine = PersistentLearningEngine()
