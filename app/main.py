from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog
from contextlib import asynccontextmanager

from app.database import engine, Base
from app.api import posts, responses, admin, social_accounts
from app.api.v1 import learning, intelligence, multimodal, multilingual
from app.services.ollama_service import OllamaService
from config.settings import settings


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Agentic Social Handler")

    # TODO: Create database tables
    # async with engine.begin() as conn:
    #     await conn.run_sync(Base.metadata.create_all)

    # TODO: Initialize Ollama service
    # ollama_service = OllamaService()
    # await ollama_service.initialize()

    logger.info("Application started successfully")

    yield

    # Shutdown
    logger.info("Shutting down Agentic Social Handler")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="An agentic system for social media sentiment analysis and response generation",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error("HTTP exception occurred", status_code=exc.status_code, detail=exc.detail)
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error("Unexpected error occurred", error=str(exc), exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error"}
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": settings.app_name}


# Demo endpoints to show system capabilities
@app.get("/demo/sentiment")
async def demo_sentiment_analysis():
    """Demo endpoint showing sentiment analysis capability"""
    return {
        "feature": "Sentiment Analysis",
        "description": "Analyzes social media posts for sentiment using local LLM",
        "example": {
            "input": "I love this product! It's amazing!",
            "output": {
                "sentiment": "positive",
                "score": 0.85,
                "confidence": 0.92
            }
        },
        "status": "Ready for integration"
    }


@app.get("/demo/response-generation")
async def demo_response_generation():
    """Demo endpoint showing response generation capability"""
    return {
        "feature": "Response Generation",
        "description": "Generates contextual responses to social media posts",
        "example": {
            "input": {
                "post": "Having trouble with your app, it keeps crashing",
                "sentiment": "negative",
                "platform": "twitter"
            },
            "output": {
                "response": "Hi! We're sorry to hear you're experiencing issues. Please DM us your device details and we'll help resolve this quickly. Thank you for your patience! 🛠️",
                "tone": "helpful",
                "confidence": 0.88
            }
        },
        "status": "Ready for integration"
    }


@app.get("/demo/workflow")
async def demo_workflow():
    """Demo endpoint showing the complete workflow"""
    return {
        "workflow": "Agentic Social Media Management",
        "steps": [
            {
                "step": 1,
                "name": "Monitor",
                "description": "Scan social media for mentions, hashtags, keywords",
                "status": "✅ Ready"
            },
            {
                "step": 2,
                "name": "Analyze",
                "description": "Perform sentiment analysis using local LLM",
                "status": "✅ Ready"
            },
            {
                "step": 3,
                "name": "Generate",
                "description": "Create contextual responses based on sentiment",
                "status": "✅ Ready"
            },
            {
                "step": 4,
                "name": "Review",
                "description": "Admin reviews and approves responses",
                "status": "✅ Ready"
            },
            {
                "step": 5,
                "name": "Publish",
                "description": "Post approved responses to social media",
                "status": "✅ Ready"
            },
            {
                "step": 6,
                "name": "Learn",
                "description": "Improve based on admin feedback",
                "status": "✅ Ready"
            }
        ],
        "features": {
            "local_llm": "Privacy-focused AI processing",
            "multi_platform": "Twitter, Instagram, extensible",
            "human_oversight": "Admin approval workflow",
            "continuous_learning": "Feedback-based improvement"
        }
    }


# Include API routers
app.include_router(posts.router, prefix="/api/v1/posts", tags=["posts"])
app.include_router(responses.router, prefix="/api/v1/responses", tags=["responses"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["admin"])
app.include_router(social_accounts.router, prefix="/api/v1/social-accounts", tags=["social-accounts"])

# Include v1 API routers
app.include_router(learning.router, prefix="/api/v1/learning", tags=["learning"])
app.include_router(intelligence.router, prefix="/api/v1/intelligence", tags=["intelligence"])
app.include_router(multimodal.router, prefix="/api/v1/multimodal", tags=["multimodal"])
app.include_router(multilingual.router, prefix="/api/v1/multilingual", tags=["multilingual"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
