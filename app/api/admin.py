from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any

from app.database import get_db
from app.models.feedback import Feedback, FeedbackType
from app.models.generated_response import GeneratedResponse
from app.schemas.feedback import Feedback<PERSON><PERSON>, FeedbackResponse

router = APIRouter()


@router.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(
    feedback_data: FeedbackCreate,
    db: AsyncSession = Depends(get_db)
):
    """Submit feedback for a generated response"""
    
    # Verify response exists
    query = select(GeneratedResponse).where(GeneratedResponse.id == feedback_data.response_id)
    result = await db.execute(query)
    response = result.scalar_one_or_none()
    
    if not response:
        raise HTTPException(status_code=404, detail="Response not found")
    
    # Create feedback
    feedback = Feedback(
        feedback_type=feedback_data.feedback_type,
        rating=feedback_data.rating,
        comments=feedback_data.comments,
        suggested_response=feedback_data.suggested_response,
        improvement_areas=feedback_data.improvement_areas,
        response_id=feedback_data.response_id,
        user_id=feedback_data.user_id,  # In real app, get from auth
        learning_weight=feedback_data.learning_weight or 1.0
    )
    
    db.add(feedback)
    await db.commit()
    await db.refresh(feedback)
    
    return FeedbackResponse.from_orm(feedback)


@router.get("/dashboard")
async def get_admin_dashboard(db: AsyncSession = Depends(get_db)):
    """Get admin dashboard statistics"""
    
    # Get pending responses count
    pending_query = select(GeneratedResponse).where(
        GeneratedResponse.status == "pending"
    )
    pending_result = await db.execute(pending_query)
    pending_count = len(pending_result.scalars().all())
    
    # Get recent feedback
    feedback_query = select(Feedback).order_by(Feedback.created_at.desc()).limit(10)
    feedback_result = await db.execute(feedback_query)
    recent_feedback = feedback_result.scalars().all()
    
    # Calculate feedback distribution
    all_feedback_query = select(Feedback)
    all_feedback_result = await db.execute(all_feedback_query)
    all_feedback = all_feedback_result.scalars().all()
    
    feedback_distribution = {}
    for feedback in all_feedback:
        feedback_type = feedback.feedback_type
        feedback_distribution[feedback_type] = feedback_distribution.get(feedback_type, 0) + 1
    
    return {
        "pending_responses": pending_count,
        "recent_feedback": [
            {
                "id": f.id,
                "type": f.feedback_type,
                "rating": f.rating,
                "comments": f.comments,
                "created_at": f.created_at,
                "response_id": f.response_id
            }
            for f in recent_feedback
        ],
        "feedback_distribution": feedback_distribution,
        "total_feedback": len(all_feedback)
    }


@router.get("/learning/status")
async def get_learning_status(db: AsyncSession = Depends(get_db)):
    """Get status of the learning system"""
    
    # Get unprocessed feedback for learning
    unprocessed_query = select(Feedback).where(
        Feedback.is_processed_for_learning == False
    )
    unprocessed_result = await db.execute(unprocessed_query)
    unprocessed_feedback = unprocessed_result.scalars().all()
    
    # Get total feedback
    total_query = select(Feedback)
    total_result = await db.execute(total_query)
    total_feedback = total_result.scalars().all()
    
    # Calculate learning metrics
    thumbs_up = len([f for f in total_feedback if f.feedback_type == FeedbackType.THUMBS_UP])
    thumbs_down = len([f for f in total_feedback if f.feedback_type == FeedbackType.THUMBS_DOWN])
    
    return {
        "unprocessed_feedback_count": len(unprocessed_feedback),
        "total_feedback_count": len(total_feedback),
        "positive_feedback": thumbs_up,
        "negative_feedback": thumbs_down,
        "learning_ready": len(unprocessed_feedback) >= 10,  # Minimum threshold
        "feedback_quality_score": thumbs_up / (thumbs_up + thumbs_down) if (thumbs_up + thumbs_down) > 0 else 0
    }


@router.post("/learning/process")
async def process_learning_feedback(db: AsyncSession = Depends(get_db)):
    """Process unprocessed feedback for learning"""
    
    # Get unprocessed feedback
    query = select(Feedback).where(Feedback.is_processed_for_learning == False)
    result = await db.execute(query)
    unprocessed_feedback = result.scalars().all()
    
    if len(unprocessed_feedback) < 10:  # Minimum threshold
        raise HTTPException(
            status_code=400, 
            detail="Insufficient feedback for learning (minimum 10 required)"
        )
    
    # Use the persistent learning engine
    try:
        from app.learning.persistent_learning_engine import learning_engine

        if not learning_engine.is_initialized:
            await learning_engine.initialize()

        result = await learning_engine.process_feedback_for_learning(db)

        return {
            "message": "Learning feedback processed using persistent learning engine",
            "data": result,
            "status": "completed"
        }

    except ImportError:
        # Fallback to simple processing if learning engine not available
        processed_count = 0
        for feedback in unprocessed_feedback:
            feedback.is_processed_for_learning = True
            processed_count += 1

        await db.commit()

        return {
            "message": "Learning feedback processed (basic mode)",
            "processed_count": processed_count,
            "status": "completed"
        }


@router.get("/system/health")
async def get_system_health():
    """Get system health status"""
    
    # TODO: Implement actual health checks
    # - Database connectivity
    # - Ollama service status
    # - Redis connectivity
    # - Social media API status
    
    return {
        "status": "healthy",
        "services": {
            "database": "healthy",
            "ollama": "healthy", 
            "redis": "healthy",
            "twitter_api": "unknown",
            "instagram_api": "unknown"
        },
        "timestamp": "2024-01-01T00:00:00Z"
    }
