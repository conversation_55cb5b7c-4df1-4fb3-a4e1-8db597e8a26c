"""
Learning API endpoints for persistent learning management
"""

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
import structlog

from app.database import get_db
from app.learning.persistent_learning_engine import learning_engine
from app.learning.model_fine_tuner import model_fine_tuner
from app.auth.dependencies import get_current_user
from app.models.user import User

logger = structlog.get_logger()
router = APIRouter()


@router.post("/process-feedback")
async def process_feedback_for_learning(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Process unprocessed feedback to extract learning rules"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        result = await learning_engine.process_feedback_for_learning(db)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to process feedback for learning: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_learning_statistics(
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive learning statistics"""
    try:
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        stats = await learning_engine.get_learning_statistics()
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get learning statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/transfer-learning")
async def transfer_learning_to_new_model(
    new_model_version: str,
    current_user: User = Depends(get_current_user)
):
    """Transfer learning when upgrading to a new model version"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        result = await learning_engine.transfer_learning_to_new_model(new_model_version)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to transfer learning: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup-rules")
async def cleanup_old_rules(
    max_age_days: int = 90,
    current_user: User = Depends(get_current_user)
):
    """Clean up old, unused learning rules"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        result = await learning_engine.cleanup_old_rules(max_age_days)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to cleanup rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/fine-tune/prepare-data")
async def prepare_fine_tuning_data(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Prepare training data for model fine-tuning"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not model_fine_tuner.tokenizer:
            await model_fine_tuner.initialize()
            
        result = await model_fine_tuner.prepare_training_data(db)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to prepare training data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/fine-tune/start")
async def start_fine_tuning(
    training_data_file: str,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Start model fine-tuning process"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not model_fine_tuner.tokenizer:
            await model_fine_tuner.initialize()
            
        if model_fine_tuner.is_training:
            raise HTTPException(status_code=400, detail="Training already in progress")
            
        # Start fine-tuning in background
        background_tasks.add_task(
            model_fine_tuner.fine_tune_model, 
            training_data_file
        )
        
        return {
            "success": True,
            "message": "Fine-tuning started in background",
            "training_data_file": training_data_file
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to start fine-tuning: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/fine-tune/status")
async def get_fine_tuning_status(
    current_user: User = Depends(get_current_user)
):
    """Get current fine-tuning status"""
    try:
        return {
            "success": True,
            "data": {
                "is_training": model_fine_tuner.is_training,
                "last_training_time": model_fine_tuner.last_training_time.isoformat() if model_fine_tuner.last_training_time else None
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get fine-tuning status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/fine-tune/models")
async def get_available_fine_tuned_models(
    current_user: User = Depends(get_current_user)
):
    """Get list of available fine-tuned models"""
    try:
        models = await model_fine_tuner.get_available_models()
        
        return {
            "success": True,
            "data": {
                "models": models,
                "count": len(models)
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/apply-to-response")
async def apply_learning_to_response(
    post_content: str,
    context: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Apply learned rules to generate better responses"""
    try:
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        learned_response = await learning_engine.apply_learning_to_response(
            post_content, 
            context
        )
        
        return {
            "success": True,
            "data": {
                "learned_response": learned_response,
                "has_learning": learned_response is not None
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to apply learning: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/knowledge-base/export")
async def export_knowledge_base(
    current_user: User = Depends(get_current_user)
):
    """Export the current knowledge base"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        # Get all learning rules
        rules_data = []
        for rule in learning_engine.learning_rules.values():
            rules_data.append({
                "rule_id": rule.rule_id,
                "pattern": rule.pattern,
                "context_type": rule.context_type,
                "response_template": rule.response_template,
                "confidence_score": rule.confidence_score,
                "usage_count": rule.usage_count,
                "success_rate": rule.success_rate,
                "created_at": rule.created_at,
                "last_updated": rule.last_updated
            })
            
        return {
            "success": True,
            "data": {
                "rules": rules_data,
                "total_rules": len(rules_data),
                "exported_at": learning_engine.current_checkpoint.timestamp if learning_engine.current_checkpoint else None
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to export knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/knowledge-base/backup")
async def create_knowledge_base_backup(
    current_user: User = Depends(get_current_user)
):
    """Create a manual backup of the knowledge base"""
    try:
        # Check if user has admin permissions
        if current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
            
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        # Force save current state
        await learning_engine._save_knowledge_base()
        await learning_engine._save_domain_knowledge()
        
        # Create checkpoint
        await learning_engine._create_checkpoint(0)
        
        return {
            "success": True,
            "message": "Knowledge base backup created",
            "checkpoint_id": learning_engine.current_checkpoint.checkpoint_id if learning_engine.current_checkpoint else None
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to create backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))
