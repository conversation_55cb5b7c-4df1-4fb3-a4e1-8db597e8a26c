#!/usr/bin/env python3
"""
Learning Persistence Test Script
Demonstrates how learning is retained across restarts and upgrades
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def simulate_learning_cycle():
    """Simulate a complete learning cycle"""
    
    print("🧪 Learning Persistence Test")
    print("=" * 40)
    
    try:
        from app.learning.persistent_learning_engine import learning_engine, LearningRule
        
        # Initialize if not already done
        if not learning_engine.is_initialized:
            await learning_engine.initialize()
            
        print("✅ Learning engine initialized")
        
        # Step 1: Create some sample learning rules
        print("\n📚 Step 1: Creating sample learning rules...")
        
        sample_rules = [
            LearningRule(
                rule_id="test_baggage_delay_empathy",
                pattern="baggage_delay",
                context_type="negative",
                response_template="We sincerely apologize for the baggage delay. We understand how frustrating this must be, especially when you need your belongings. Let us help resolve this immediately.",
                confidence_score=0.85,
                usage_count=5,
                success_rate=0.9,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
                feedback_sources=[1, 2, 3]
            ),
            LearningRule(
                rule_id="test_flight_delay_compensation",
                pattern="flight_delay",
                context_type="negative",
                response_template="We apologize for the flight delay. Due to the extended delay, you're eligible for meal vouchers and accommodation if needed. Please visit our service desk for assistance.",
                confidence_score=0.92,
                usage_count=8,
                success_rate=0.95,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
                feedback_sources=[4, 5, 6, 7]
            ),
            LearningRule(
                rule_id="test_positive_feedback_thanks",
                pattern="general_compliment",
                context_type="positive",
                response_template="Thank you so much for your kind words! We're delighted to hear about your positive experience. Your feedback motivates our team to continue providing excellent service.",
                confidence_score=0.88,
                usage_count=12,
                success_rate=0.98,
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
                feedback_sources=[8, 9, 10, 11, 12]
            )
        ]
        
        # Add rules to learning engine
        for rule in sample_rules:
            learning_engine.learning_rules[rule.rule_id] = rule
            print(f"   ✅ Added rule: {rule.rule_id}")
            
        # Step 2: Save knowledge base
        print("\n💾 Step 2: Saving knowledge base...")
        await learning_engine._save_knowledge_base()
        print("   ✅ Knowledge base saved")
        
        # Step 3: Create checkpoint
        print("\n📊 Step 3: Creating checkpoint...")
        await learning_engine._create_checkpoint(len(sample_rules))
        print("   ✅ Checkpoint created")
        
        # Step 4: Get current statistics
        print("\n📈 Step 4: Current learning statistics...")
        stats = await learning_engine.get_learning_statistics()
        print(f"   📚 Total Rules: {stats['total_rules']}")
        print(f"   🎯 Avg Confidence: {stats['avg_confidence']:.2f}")
        print(f"   📊 Avg Success Rate: {stats['avg_success_rate']:.2f}")
        print(f"   🔄 Total Usage: {stats['total_usage']}")
        
        # Step 5: Test learning application
        print("\n🧪 Step 5: Testing learning application...")
        
        test_cases = [
            {
                "content": "My baggage is delayed and I'm very upset!",
                "context": {"sentiment": "negative", "issue_type": "baggage_delay"}
            },
            {
                "content": "Flight is delayed again, this is terrible!",
                "context": {"sentiment": "negative", "issue_type": "flight_delay"}
            },
            {
                "content": "Thank you for the excellent service!",
                "context": {"sentiment": "positive", "issue_type": "general_compliment"}
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   Test {i}: {test_case['content'][:50]}...")
            
            learned_response = await learning_engine.apply_learning_to_response(
                test_case["content"],
                test_case["context"]
            )
            
            if learned_response:
                print(f"   ✅ Applied learning: {learned_response[:80]}...")
            else:
                print("   ℹ️  No specific learning rule matched")
                
        return True
        
    except Exception as e:
        print(f"❌ Error in learning cycle: {e}")
        return False


async def simulate_restart():
    """Simulate application restart and verify learning persistence"""
    
    print("\n🔄 Simulating Application Restart...")
    print("=" * 40)
    
    try:
        # Clear the current learning engine state (simulate restart)
        from app.learning.persistent_learning_engine import learning_engine
        
        # Store current stats for comparison
        old_stats = await learning_engine.get_learning_statistics()
        old_rules_count = old_stats['total_rules']
        
        print(f"📊 Before restart: {old_rules_count} learning rules")
        
        # Clear in-memory state
        learning_engine.learning_rules.clear()
        learning_engine.is_initialized = False
        learning_engine.current_checkpoint = None
        
        print("🧹 Cleared in-memory state (simulating restart)")
        
        # Re-initialize (this should load from persistent storage)
        print("🔄 Re-initializing learning engine...")
        await learning_engine.initialize()
        
        # Get new stats
        new_stats = await learning_engine.get_learning_statistics()
        new_rules_count = new_stats['total_rules']
        
        print(f"📊 After restart: {new_rules_count} learning rules")
        
        # Verify persistence
        if new_rules_count == old_rules_count:
            print("✅ SUCCESS: All learning rules persisted across restart!")
            
            # Test that learning still works
            print("\n🧪 Testing learning application after restart...")
            
            learned_response = await learning_engine.apply_learning_to_response(
                "My baggage is delayed and I'm frustrated!",
                {"sentiment": "negative", "issue_type": "baggage_delay"}
            )
            
            if learned_response:
                print("✅ Learning application works after restart!")
                print(f"   Response: {learned_response[:100]}...")
                return True
            else:
                print("⚠️  Learning rules loaded but application failed")
                return False
        else:
            print(f"❌ FAILED: Lost {old_rules_count - new_rules_count} learning rules!")
            return False
            
    except Exception as e:
        print(f"❌ Error in restart simulation: {e}")
        return False


async def simulate_model_upgrade():
    """Simulate model upgrade and verify learning transfer"""
    
    print("\n🔄 Simulating Model Upgrade...")
    print("=" * 40)
    
    try:
        from app.learning.persistent_learning_engine import learning_engine
        
        # Get current state
        old_stats = await learning_engine.get_learning_statistics()
        old_model_version = old_stats.get('current_checkpoint', {}).get('model_version', 'unknown')
        
        print(f"📊 Current model: {old_model_version}")
        print(f"📚 Current rules: {old_stats['total_rules']}")
        
        # Simulate model upgrade
        new_model_version = "microsoft/Phi-3-mini-4k-instruct-v2.0"
        print(f"🔄 Upgrading to: {new_model_version}")
        
        # Transfer learning
        transfer_result = await learning_engine.transfer_learning_to_new_model(new_model_version)
        
        if transfer_result['success']:
            print("✅ Learning transfer successful!")
            print(f"   Transferred rules: {transfer_result['transferred_rules']}")
            print(f"   Backup created: {transfer_result['backup_checkpoint']}")
            
            # Verify new state
            new_stats = await learning_engine.get_learning_statistics()
            new_checkpoint = new_stats.get('current_checkpoint', {})
            
            print(f"📊 New model version: {new_checkpoint.get('model_version', 'unknown')}")
            print(f"📚 Rules after transfer: {new_stats['total_rules']}")
            
            # Test learning still works
            learned_response = await learning_engine.apply_learning_to_response(
                "Flight delayed, need compensation info",
                {"sentiment": "negative", "issue_type": "flight_delay"}
            )
            
            if learned_response:
                print("✅ Learning works with new model version!")
                return True
            else:
                print("⚠️  Learning transfer completed but application failed")
                return False
        else:
            print(f"❌ Learning transfer failed: {transfer_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error in model upgrade simulation: {e}")
        return False


async def verify_file_persistence():
    """Verify that learning files are actually created and persistent"""
    
    print("\n📁 Verifying File Persistence...")
    print("=" * 40)
    
    try:
        from app.learning.persistent_learning_engine import learning_engine
        from config.settings import settings
        
        # Check knowledge base files
        knowledge_base_path = Path(settings.knowledge_base_path)
        rulebook_path = Path(settings.rulebook_path)
        checkpoints_path = Path(settings.learning_checkpoints_path)
        
        print(f"📁 Knowledge base directory: {knowledge_base_path}")
        print(f"   Exists: {'✅' if knowledge_base_path.exists() else '❌'}")
        
        print(f"📚 Rulebook file: {rulebook_path}")
        print(f"   Exists: {'✅' if rulebook_path.exists() else '❌'}")
        
        if rulebook_path.exists():
            with open(rulebook_path, 'r') as f:
                rulebook_data = json.load(f)
            print(f"   Rules in file: {len(rulebook_data.get('rules', []))}")
            print(f"   Last updated: {rulebook_data.get('last_updated', 'Unknown')}")
        
        print(f"📊 Checkpoints directory: {checkpoints_path}")
        print(f"   Exists: {'✅' if checkpoints_path.exists() else '❌'}")
        
        if checkpoints_path.exists():
            checkpoint_files = list(checkpoints_path.glob("checkpoint_*.json"))
            print(f"   Checkpoint files: {len(checkpoint_files)}")
            
            if checkpoint_files:
                latest_checkpoint = max(checkpoint_files, key=lambda p: p.stat().st_mtime)
                print(f"   Latest: {latest_checkpoint.name}")
        
        # Check domain knowledge
        domain_knowledge_path = knowledge_base_path / "domain_knowledge.json"
        print(f"🧠 Domain knowledge: {domain_knowledge_path}")
        print(f"   Exists: {'✅' if domain_knowledge_path.exists() else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying file persistence: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🧪 LEARNING PERSISTENCE TEST SUITE")
    print("=" * 50)
    print("This test demonstrates how learning is retained across:")
    print("• Application restarts")
    print("• Model upgrades")
    print("• System reboots")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Error: Please run this script from the project root directory")
        return False
        
    test_results = []
    
    # Test 1: Learning cycle
    print("\n🧪 TEST 1: Learning Cycle")
    result1 = await simulate_learning_cycle()
    test_results.append(("Learning Cycle", result1))
    
    if not result1:
        print("❌ Learning cycle failed, stopping tests")
        return False
        
    # Test 2: Restart simulation
    print("\n🧪 TEST 2: Restart Simulation")
    result2 = await simulate_restart()
    test_results.append(("Restart Persistence", result2))
    
    # Test 3: Model upgrade simulation
    print("\n🧪 TEST 3: Model Upgrade Simulation")
    result3 = await simulate_model_upgrade()
    test_results.append(("Model Upgrade Transfer", result3))
    
    # Test 4: File persistence verification
    print("\n🧪 TEST 4: File Persistence Verification")
    result4 = await verify_file_persistence()
    test_results.append(("File Persistence", result4))
    
    # Summary
    print("\n📋 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 30)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Learning persistence is working correctly!")
        print("✅ Your system will retain learning across restarts and upgrades!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n⚠️  Please check the configuration and try again")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
